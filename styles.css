/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-pink: #ff6b9d;
    --secondary-pink: #ffc3d8;
    --light-pink: #fff0f5;
    --purple: #9b59b6;
    --light-purple: #e8d5f2;
    --gold: #f39c12;
    --white: #ffffff;
    --text-dark: #2c3e50;
    --text-light: #7f8c8d;
    --gradient-primary: linear-gradient(135deg, #ff6b9d, #9b59b6);
    --gradient-secondary: linear-gradient(135deg, #ffc3d8, #e8d5f2);
    --shadow: 0 10px 30px rgba(255, 107, 157, 0.3);
    --shadow-hover: 0 15px 40px rgba(255, 107, 157, 0.4);
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background: var(--gradient-secondary);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Floating Hearts Animation */
.floating-hearts {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.heart {
    position: absolute;
    color: var(--primary-pink);
    font-size: 20px;
    animation: float 6s ease-in-out infinite;
    opacity: 0.7;
}

.heart::before {
    content: '♥';
}

.heart:nth-child(1) {
    left: 10%;
    animation-delay: 0s;
    animation-duration: 6s;
}

.heart:nth-child(2) {
    left: 20%;
    animation-delay: 1s;
    animation-duration: 8s;
}

.heart:nth-child(3) {
    left: 30%;
    animation-delay: 2s;
    animation-duration: 7s;
}

.heart:nth-child(4) {
    left: 70%;
    animation-delay: 3s;
    animation-duration: 9s;
}

.heart:nth-child(5) {
    left: 80%;
    animation-delay: 4s;
    animation-duration: 6s;
}

.heart:nth-child(6) {
    left: 90%;
    animation-delay: 5s;
    animation-duration: 8s;
}

@keyframes float {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 0.7;
    }
    90% {
        opacity: 0.7;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-family: 'Dancing Script', cursive;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-pink);
}

.nav-logo i {
    margin-right: 0.5rem;
    animation: pulse 2s infinite;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.nav-link:hover {
    color: var(--primary-pink);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: var(--gradient-primary);
    color: var(--white);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hearts" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><text x="10" y="15" text-anchor="middle" fill="rgba(255,255,255,0.1)" font-size="12">♥</text></pattern></defs><rect width="100" height="100" fill="url(%23hearts)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
    animation: fadeInUp 1s ease-out;
}

.main-title {
    font-family: 'Dancing Script', cursive;
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    animation: slideInDown 1s ease-out;
}

.sub-title {
    font-family: 'Dancing Script', cursive;
    font-size: 2.5rem;
    font-weight: 600;
    margin-bottom: 2rem;
    animation: slideInUp 1s ease-out 0.3s both;
}

.heart-divider {
    margin: 2rem 0;
    animation: fadeIn 1s ease-out 0.6s both;
}

.heart-divider i {
    margin: 0 0.5rem;
    font-size: 1.5rem;
    animation: pulse 2s infinite;
}

.heart-divider i:nth-child(2) {
    animation-delay: 0.3s;
}

.heart-divider i:nth-child(3) {
    animation-delay: 0.6s;
}

.birthday-message {
    font-size: 1.2rem;
    max-width: 600px;
    margin: 0 auto 3rem;
    line-height: 1.8;
    animation: fadeIn 1s ease-out 0.9s both;
}

.countdown-container {
    animation: fadeInUp 1s ease-out 1.2s both;
}

.countdown-container h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    font-family: 'Dancing Script', cursive;
}

.countdown {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.time-unit {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    padding: 1.5rem;
    border-radius: 15px;
    min-width: 80px;
    transition: transform 0.3s ease;
}

.time-unit:hover {
    transform: translateY(-5px);
}

.time-unit span {
    display: block;
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.time-unit label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    animation: bounce 2s infinite;
    cursor: pointer;
}

.scroll-indicator i {
    font-size: 2rem;
    color: var(--white);
}

/* Section Titles */
.section-title {
    font-family: 'Dancing Script', cursive;
    font-size: 3rem;
    text-align: center;
    margin-bottom: 3rem;
    color: var(--primary-pink);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

/* Memories Section */
.memories {
    padding: 5rem 0;
    background: var(--white);
}

.memories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.memory-card {
    background: var(--light-pink);
    padding: 2.5rem;
    border-radius: 20px;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: var(--shadow);
    position: relative;
    overflow: hidden;
}

.memory-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
}

.memory-card:hover::before {
    left: 100%;
}

.memory-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
}

.memory-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: var(--white);
    font-size: 2rem;
}

.memory-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.memory-card p {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.memory-date {
    color: var(--primary-pink);
    font-weight: 500;
    font-style: italic;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* Gallery Section */
.gallery {
    padding: 5rem 0;
    background: var(--light-purple);
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.gallery-item {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    aspect-ratio: 1;
    cursor: pointer;
    transition: all 0.3s ease;
}

.gallery-item:hover {
    transform: scale(1.05);
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 107, 157, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-overlay i {
    color: var(--white);
    font-size: 3rem;
    animation: pulse 1.5s infinite;
}

/* Quotes Section */
.quotes {
    padding: 5rem 0;
    background: var(--white);
}

.quotes-container {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
    height: 300px;
    overflow: hidden;
}

.quote-card {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--light-pink);
    padding: 3rem;
    border-radius: 20px;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.5s ease;
    box-shadow: var(--shadow);
}

.quote-card.active {
    opacity: 1;
    transform: translateX(0);
}

.quote-icon {
    margin-bottom: 1.5rem;
}

.quote-icon i {
    font-size: 3rem;
    color: var(--primary-pink);
}

.quote-text {
    font-size: 1.3rem;
    line-height: 1.8;
    margin-bottom: 2rem;
    color: var(--text-dark);
    font-style: italic;
}

.quote-author {
    color: var(--primary-pink);
    font-weight: 600;
    font-family: 'Dancing Script', cursive;
    font-size: 1.2rem;
}

.quote-navigation {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2rem;
    margin-top: 2rem;
}

.quote-btn {
    background: var(--gradient-primary);
    border: none;
    color: var(--white);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quote-btn:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow);
}

.quote-dots {
    display: flex;
    gap: 0.5rem;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--text-light);
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active {
    background: var(--primary-pink);
    transform: scale(1.2);
}

/* Footer */
.footer {
    background: var(--gradient-primary);
    color: var(--white);
    padding: 3rem 0;
    text-align: center;
}

.footer-content p {
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.footer-content i {
    color: var(--gold);
    animation: pulse 2s infinite;
}

.footer-date {
    font-family: 'Dancing Script', cursive;
    font-size: 1.5rem;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        gap: 1rem;
    }

    .main-title {
        font-size: 2.5rem;
    }

    .sub-title {
        font-size: 1.8rem;
    }

    .birthday-message {
        font-size: 1rem;
        padding: 0 1rem;
    }

    .countdown {
        gap: 1rem;
    }

    .time-unit {
        padding: 1rem;
        min-width: 60px;
    }

    .time-unit span {
        font-size: 1.5rem;
    }

    .section-title {
        font-size: 2.5rem;
    }

    .memories-grid {
        grid-template-columns: 1fr;
    }

    .memory-card {
        padding: 2rem;
    }

    .gallery-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }

    .quote-card {
        padding: 2rem;
    }

    .quote-text {
        font-size: 1.1rem;
    }

    .quote-navigation {
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .nav-container {
        flex-direction: column;
        gap: 1rem;
    }

    .nav-menu {
        flex-wrap: wrap;
        justify-content: center;
    }

    .main-title {
        font-size: 2rem;
    }

    .sub-title {
        font-size: 1.5rem;
    }

    .countdown {
        flex-direction: column;
        align-items: center;
    }

    .gallery-grid {
        grid-template-columns: 1fr 1fr;
    }
}
