/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-pink: #ff6b9d;
    --secondary-pink: #ffc3d8;
    --light-pink: #fff0f5;
    --purple: #9b59b6;
    --light-purple: #e8d5f2;
    --gold: #f39c12;
    --white: #ffffff;
    --text-dark: #2c3e50;
    --text-light: #7f8c8d;
    --gradient-primary: linear-gradient(135deg, #ff6b9d, #9b59b6);
    --gradient-secondary: linear-gradient(135deg, #ffc3d8, #e8d5f2);
    --shadow: 0 10px 30px rgba(255, 107, 157, 0.3);
    --shadow-hover: 0 15px 40px rgba(255, 107, 157, 0.4);
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background: var(--gradient-secondary);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Floating Hearts Animation */
.floating-hearts {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.heart {
    position: absolute;
    color: var(--primary-pink);
    font-size: 20px;
    animation: float 6s ease-in-out infinite;
    opacity: 0.7;
}

.heart::before {
    content: '♥';
}

.heart:nth-child(1) {
    left: 10%;
    animation-delay: 0s;
    animation-duration: 6s;
}

.heart:nth-child(2) {
    left: 20%;
    animation-delay: 1s;
    animation-duration: 8s;
}

.heart:nth-child(3) {
    left: 30%;
    animation-delay: 2s;
    animation-duration: 7s;
}

.heart:nth-child(4) {
    left: 70%;
    animation-delay: 3s;
    animation-duration: 9s;
}

.heart:nth-child(5) {
    left: 80%;
    animation-delay: 4s;
    animation-duration: 6s;
}

.heart:nth-child(6) {
    left: 90%;
    animation-delay: 5s;
    animation-duration: 8s;
}

@keyframes float {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 0.7;
    }
    90% {
        opacity: 0.7;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-family: 'Dancing Script', cursive;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-pink);
}

.nav-logo i {
    margin-right: 0.5rem;
    animation: pulse 2s infinite;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.nav-link:hover {
    color: var(--primary-pink);
}

/* New Hero Section */
.hero {
    min-height: 100vh;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    display: flex;
    align-items: center;
    padding: 0;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.gradient-orb {
    position: absolute;
    border-radius: 50%;
    filter: blur(40px);
    animation: float 6s ease-in-out infinite;
}

.orb-1 {
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(255, 107, 157, 0.4), transparent);
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.orb-2 {
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(155, 89, 182, 0.3), transparent);
    top: 60%;
    right: 20%;
    animation-delay: 2s;
}

.orb-3 {
    width: 250px;
    height: 250px;
    background: radial-gradient(circle, rgba(255, 195, 216, 0.3), transparent);
    bottom: 20%;
    left: 30%;
    animation-delay: 4s;
}

.floating-particles {
    position: absolute;
    width: 100%;
    height: 100%;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    animation: particleFloat 8s linear infinite;
}

.particle:nth-child(1) { left: 10%; animation-delay: 0s; animation-duration: 8s; }
.particle:nth-child(2) { left: 20%; animation-delay: 1s; animation-duration: 10s; }
.particle:nth-child(3) { left: 30%; animation-delay: 2s; animation-duration: 7s; }
.particle:nth-child(4) { left: 40%; animation-delay: 3s; animation-duration: 9s; }
.particle:nth-child(5) { left: 60%; animation-delay: 4s; animation-duration: 8s; }
.particle:nth-child(6) { left: 70%; animation-delay: 5s; animation-duration: 11s; }
.particle:nth-child(7) { left: 80%; animation-delay: 6s; animation-duration: 6s; }
.particle:nth-child(8) { left: 90%; animation-delay: 7s; animation-duration: 9s; }

.hero-container {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    min-height: 100vh;
}

.hero-left {
    color: white;
    animation: slideInLeft 1s ease-out;
}

.birthday-badge {
    display: inline-block;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    padding: 0.8rem 1.5rem;
    border-radius: 50px;
    margin-bottom: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    animation: fadeInDown 1s ease-out 0.3s both;
}

.badge-text {
    font-size: 1rem;
    font-weight: 500;
}

.hero-title-section {
    margin-bottom: 2rem;
}

.hero-main-title {
    font-family: 'Dancing Script', cursive;
    font-size: 5rem;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.title-line-1 {
    display: block;
    animation: slideInDown 1s ease-out 0.6s both;
}

.title-line-2 {
    display: block;
    background: linear-gradient(45deg, #FFD700, #FF69B4, #FF1493);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: slideInUp 1s ease-out 0.9s both;
}

.title-decoration {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
    animation: fadeIn 1s ease-out 1.2s both;
}

.decoration-line {
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, transparent, #FFD700, transparent);
}

.decoration-heart {
    font-size: 1.5rem;
    color: #FF69B4;
    animation: pulse 2s infinite;
}

.hero-message {
    font-size: 1.3rem;
    line-height: 1.8;
    margin-bottom: 3rem;
    opacity: 0.95;
    animation: fadeInUp 1s ease-out 1.5s both;
}

.hero-actions {
    display: flex;
    gap: 1.5rem;
    animation: fadeInUp 1s ease-out 1.8s both;
    position: relative;
    z-index: 10;
}

.primary-btn {
    background: linear-gradient(45deg, #FF69B4, #FF1493);
    border: none;
    color: white;
    padding: 1.2rem 2.5rem;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(255, 105, 180, 0.4);
    display: flex;
    align-items: center;
    gap: 0.8rem;
    z-index: 10;
    pointer-events: auto;
    user-select: none;
}

.primary-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(255, 105, 180, 0.6);
}

.secondary-btn {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 1.2rem 2.5rem;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.secondary-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-3px);
}

.btn-glow {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
}

.primary-btn:hover .btn-glow {
    left: 100%;
}

.hero-right {
    animation: slideInRight 1s ease-out;
}

.celebration-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    padding: 2.5rem;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    animation: fadeInUp 1s ease-out 0.6s both;
}

.card-header h3 {
    font-family: 'Dancing Script', cursive;
    font-size: 1.8rem;
    color: white;
    text-align: center;
    margin-bottom: 2rem;
}

.countdown-section h4 {
    color: white;
    font-size: 1.1rem;
    text-align: center;
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.countdown-display {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.time-block {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1rem;
    text-align: center;
    min-width: 60px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.time-number {
    display: block;
    font-size: 1.8rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.3rem;
}

.time-label {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.8);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.time-separator {
    font-size: 1.5rem;
    color: white;
    font-weight: bold;
}

.birthday-stats {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: white;
    padding: 0.8rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateX(5px);
}

.stat-icon {
    font-size: 1.3rem;
}

.stat-text {
    font-size: 1rem;
    font-weight: 500;
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.floating-balloon {
    position: absolute;
    font-size: 2rem;
    animation: balloonFloat 4s ease-in-out infinite;
}

.balloon-1 {
    top: 20%;
    right: 10%;
    animation-delay: 0s;
}

.balloon-2 {
    top: 60%;
    right: 5%;
    animation-delay: 1.5s;
}

.balloon-3 {
    top: 40%;
    right: 15%;
    animation-delay: 3s;
}

.floating-gift {
    position: absolute;
    top: 70%;
    right: 20%;
    font-size: 1.8rem;
    animation: giftBounce 3s ease-in-out infinite;
}

.floating-star {
    position: absolute;
    font-size: 1.5rem;
    animation: starTwinkle 2s ease-in-out infinite;
}

.star-1 {
    top: 30%;
    right: 25%;
    animation-delay: 0s;
}

.star-2 {
    top: 80%;
    right: 8%;
    animation-delay: 1s;
}

.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: white;
    animation: bounce 2s infinite;
    cursor: pointer;
    z-index: 3;
}

.scroll-text {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    opacity: 0.8;
}

.scroll-arrow i {
    font-size: 1.5rem;
}

/* Hero Animations */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes particleFloat {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes balloonFloat {
    0%, 100% {
        transform: translateY(0px) rotate(-2deg);
    }
    50% {
        transform: translateY(-15px) rotate(2deg);
    }
}

@keyframes giftBounce {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes starTwinkle {
    0%, 100% {
        opacity: 0.5;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.2);
    }
}

/* Section Titles */
.section-title {
    font-family: 'Dancing Script', cursive;
    font-size: 3rem;
    text-align: center;
    margin-bottom: 3rem;
    color: var(--primary-pink);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

/* Memories Section */
.memories {
    padding: 5rem 0;
    background: var(--white);
}

.memories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.memory-card {
    background: var(--light-pink);
    padding: 2.5rem;
    border-radius: 20px;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: var(--shadow);
    position: relative;
    overflow: hidden;
}

.memory-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
}

.memory-card:hover::before {
    left: 100%;
}

.memory-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
}

.memory-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: var(--white);
    font-size: 2rem;
}

.memory-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.memory-card p {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.memory-date {
    color: var(--primary-pink);
    font-weight: 500;
    font-style: italic;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* Gallery Section */
.gallery {
    padding: 5rem 0;
    background: var(--light-purple);
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.gallery-item {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    aspect-ratio: 1;
    cursor: pointer;
    transition: all 0.3s ease;
}

.gallery-item:hover {
    transform: scale(1.05);
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 107, 157, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-overlay i {
    color: var(--white);
    font-size: 3rem;
    animation: pulse 1.5s infinite;
}

/* Quotes Section */
.quotes {
    padding: 5rem 0;
    background: var(--white);
}

.quotes-container {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
    height: 300px;
    overflow: hidden;
}

.quote-card {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--light-pink);
    padding: 3rem;
    border-radius: 20px;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.5s ease;
    box-shadow: var(--shadow);
}

.quote-card.active {
    opacity: 1;
    transform: translateX(0);
}

.quote-icon {
    margin-bottom: 1.5rem;
}

.quote-icon i {
    font-size: 3rem;
    color: var(--primary-pink);
}

.quote-text {
    font-size: 1.3rem;
    line-height: 1.8;
    margin-bottom: 2rem;
    color: var(--text-dark);
    font-style: italic;
}

.quote-author {
    color: var(--primary-pink);
    font-weight: 600;
    font-family: 'Dancing Script', cursive;
    font-size: 1.2rem;
}

.quote-navigation {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2rem;
    margin-top: 2rem;
}

.quote-btn {
    background: var(--gradient-primary);
    border: none;
    color: var(--white);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quote-btn:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow);
}

.quote-dots {
    display: flex;
    gap: 0.5rem;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--text-light);
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active {
    background: var(--primary-pink);
    transform: scale(1.2);
}

/* Footer */
.footer {
    background: var(--gradient-primary);
    color: var(--white);
    padding: 3rem 0;
    text-align: center;
}

.footer-content p {
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.footer-content i {
    color: var(--gold);
    animation: pulse 2s infinite;
}

.footer-date {
    font-family: 'Dancing Script', cursive;
    font-size: 1.5rem;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .hero-container {
        gap: 2rem;
        padding: 1.5rem;
    }

    .hero-main-title {
        font-size: 4rem;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        gap: 1rem;
    }

    .hero-container {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
        padding: 1rem;
    }

    .hero-main-title {
        font-size: 3.5rem;
    }

    .hero-message {
        font-size: 1.1rem;
    }

    .hero-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .primary-btn, .secondary-btn {
        padding: 1rem 2rem;
        font-size: 1rem;
    }

    .celebration-card {
        padding: 2rem;
    }

    .countdown-display {
        gap: 0.3rem;
    }

    .time-block {
        padding: 0.8rem;
        min-width: 50px;
    }

    .time-number {
        font-size: 1.5rem;
    }

    .floating-elements {
        display: none;
    }

    .section-title {
        font-size: 2.5rem;
    }

    .memories-grid {
        grid-template-columns: 1fr;
    }

    .memory-card {
        padding: 2rem;
    }

    .gallery-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }

    .quote-card {
        padding: 2rem;
    }

    .quote-text {
        font-size: 1.1rem;
    }

    .quote-navigation {
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .nav-container {
        flex-direction: column;
        gap: 1rem;
    }

    .nav-menu {
        flex-wrap: wrap;
        justify-content: center;
    }

    .main-title {
        font-size: 2rem;
    }

    .sub-title {
        font-size: 1.5rem;
    }

    .countdown {
        flex-direction: column;
        align-items: center;
    }

    .gallery-grid {
        grid-template-columns: 1fr 1fr;
    }
}



/* Cake Animation Overlay */
.cake-animation-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.cake-animation-overlay.active {
    opacity: 1;
    visibility: visible;
}

.cake-animation-container {
    position: relative;
    text-align: center;
    max-width: 90%;
    max-height: 90%;
}

.cake-close-btn {
    position: absolute;
    top: -50px;
    right: -50px;
    width: 40px;
    height: 40px;
    background: var(--primary-pink);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10001;
}

.cake-close-btn:hover {
    transform: scale(1.1);
    background: var(--purple);
}

.cake-message {
    margin-bottom: 2rem;
    color: var(--white);
    animation: fadeInDown 0.8s ease-out;
}

.cake-message h2 {
    font-family: 'Dancing Script', cursive;
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--primary-pink);
}

.cake-message p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Birthday Cake */
.birthday-cake {
    position: relative;
    margin: 2rem auto;
    animation: cakeAppear 1s ease-out 0.5s both;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.cake-layer {
    border-radius: 10px;
    position: relative;
    margin-bottom: -10px;
}

.cake-top {
    width: 120px;
    height: 40px;
    background: var(--light-pink);
    box-shadow: 0 5px 15px rgba(255, 195, 216, 0.4);
    z-index: 3;
}

.cake-middle {
    width: 160px;
    height: 50px;
    background: var(--gradient-primary);
    box-shadow: 0 5px 15px rgba(255, 107, 157, 0.4);
    z-index: 2;
}

.cake-bottom {
    width: 200px;
    height: 60px;
    background: linear-gradient(45deg, #8B4513, #A0522D);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    z-index: 1;
    margin-bottom: 0;
}

.cake-decoration {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
}

.decoration-dot {
    position: absolute;
    width: 8px;
    height: 8px;
    background: var(--gold);
    border-radius: 50%;
    animation: decorationTwinkle 2s infinite;
}

.decoration-dot:nth-child(1) {
    top: 20%;
    left: 25%;
    animation-delay: 0s;
}

.decoration-dot:nth-child(2) {
    top: 30%;
    right: 25%;
    animation-delay: 0.4s;
}

.decoration-dot:nth-child(3) {
    top: 60%;
    left: 20%;
    animation-delay: 0.8s;
}

.decoration-dot:nth-child(4) {
    top: 70%;
    right: 20%;
    animation-delay: 1.2s;
}

.decoration-dot:nth-child(5) {
    bottom: 20%;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: 1.6s;
}

/* Candles */
.candles {
    position: absolute;
    top: -35px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 15px;
    z-index: 4;
}

.candle {
    position: relative;
    animation: candleAppear 0.5s ease-out;
}

.candle:nth-child(1) { animation-delay: 1s; }
.candle:nth-child(2) { animation-delay: 1.2s; }
.candle:nth-child(3) { animation-delay: 1.4s; }
.candle:nth-child(4) { animation-delay: 1.6s; }
.candle:nth-child(5) { animation-delay: 1.8s; }

.candle-stick {
    width: 6px;
    height: 25px;
    background: linear-gradient(to bottom, #FFB6C1, #FF69B4);
    border-radius: 3px;
    margin: 0 auto;
}

.flame {
    width: 8px;
    height: 12px;
    background: radial-gradient(circle, #FFD700, #FF4500);
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    margin: -2px auto 0;
    animation: flameFlicker 0.5s ease-in-out infinite alternate;
    box-shadow: 0 0 10px #FFD700;
}

@keyframes cakeAppear {
    0% {
        opacity: 0;
        transform: scale(0.5) translateY(50px);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes candleAppear {
    0% {
        opacity: 0;
        transform: translateY(-20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes flameFlicker {
    0% {
        transform: scale(1) rotate(-1deg);
    }
    100% {
        transform: scale(1.1) rotate(1deg);
    }
}

@keyframes decorationTwinkle {
    0%, 100% {
        opacity: 0.5;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.2);
    }
}

/* Confetti Animation */
.cake-confetti {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.confetti-piece {
    position: absolute;
    width: 10px;
    height: 10px;
    background: var(--primary-pink);
    animation: confettiFall 3s linear infinite;
    opacity: 0;
}

.confetti-piece:nth-child(odd) {
    background: var(--purple);
}

.confetti-piece:nth-child(3n) {
    background: var(--gold);
}

.confetti-piece:nth-child(4n) {
    background: var(--secondary-pink);
    border-radius: 50%;
}

.confetti-piece:nth-child(1) { left: 10%; animation-delay: 0s; animation-duration: 3s; }
.confetti-piece:nth-child(2) { left: 20%; animation-delay: 0.2s; animation-duration: 3.5s; }
.confetti-piece:nth-child(3) { left: 30%; animation-delay: 0.4s; animation-duration: 2.8s; }
.confetti-piece:nth-child(4) { left: 40%; animation-delay: 0.6s; animation-duration: 3.2s; }
.confetti-piece:nth-child(5) { left: 50%; animation-delay: 0.8s; animation-duration: 2.9s; }
.confetti-piece:nth-child(6) { left: 60%; animation-delay: 1s; animation-duration: 3.1s; }
.confetti-piece:nth-child(7) { left: 70%; animation-delay: 1.2s; animation-duration: 3.3s; }
.confetti-piece:nth-child(8) { left: 80%; animation-delay: 1.4s; animation-duration: 2.7s; }
.confetti-piece:nth-child(9) { left: 90%; animation-delay: 1.6s; animation-duration: 3.4s; }
.confetti-piece:nth-child(10) { left: 15%; animation-delay: 1.8s; animation-duration: 2.6s; }
.confetti-piece:nth-child(11) { left: 25%; animation-delay: 2s; animation-duration: 3.6s; }
.confetti-piece:nth-child(12) { left: 35%; animation-delay: 2.2s; animation-duration: 2.5s; }
.confetti-piece:nth-child(13) { left: 45%; animation-delay: 2.4s; animation-duration: 3.7s; }
.confetti-piece:nth-child(14) { left: 55%; animation-delay: 2.6s; animation-duration: 2.4s; }
.confetti-piece:nth-child(15) { left: 65%; animation-delay: 2.8s; animation-duration: 3.8s; }
.confetti-piece:nth-child(16) { left: 75%; animation-delay: 3s; animation-duration: 2.3s; }
.confetti-piece:nth-child(17) { left: 85%; animation-delay: 3.2s; animation-duration: 3.9s; }
.confetti-piece:nth-child(18) { left: 95%; animation-delay: 3.4s; animation-duration: 2.2s; }
.confetti-piece:nth-child(19) { left: 5%; animation-delay: 3.6s; animation-duration: 4s; }
.confetti-piece:nth-child(20) { left: 50%; animation-delay: 3.8s; animation-duration: 2.1s; }

@keyframes confettiFall {
    0% {
        opacity: 1;
        transform: translateY(-100vh) rotate(0deg);
    }
    100% {
        opacity: 0;
        transform: translateY(100vh) rotate(720deg);
    }
}

/* Sparkles */
.cake-sparkles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.sparkle-particle {
    position: absolute;
    width: 6px;
    height: 6px;
    background: var(--gold);
    border-radius: 50%;
    animation: sparkleFloat 4s ease-in-out infinite;
    opacity: 0;
}

.sparkle-particle:nth-child(1) { top: 20%; left: 20%; animation-delay: 0s; }
.sparkle-particle:nth-child(2) { top: 30%; right: 25%; animation-delay: 0.5s; }
.sparkle-particle:nth-child(3) { top: 50%; left: 15%; animation-delay: 1s; }
.sparkle-particle:nth-child(4) { top: 60%; right: 20%; animation-delay: 1.5s; }
.sparkle-particle:nth-child(5) { top: 80%; left: 30%; animation-delay: 2s; }
.sparkle-particle:nth-child(6) { top: 70%; right: 30%; animation-delay: 2.5s; }
.sparkle-particle:nth-child(7) { top: 40%; left: 80%; animation-delay: 3s; }
.sparkle-particle:nth-child(8) { top: 90%; right: 40%; animation-delay: 3.5s; }

@keyframes sparkleFloat {
    0%, 100% {
        opacity: 0;
        transform: translateY(0) scale(0);
    }
    50% {
        opacity: 1;
        transform: translateY(-20px) scale(1);
    }
}

/* Responsive Design for Cake */
@media (max-width: 768px) {
    .cake-surprise-btn {
        padding: 1.2rem 2rem;
        font-size: 1.1rem;
    }

    .cake-message h2 {
        font-size: 2rem;
    }

    .cake-top {
        width: 90px;
        height: 30px;
    }

    .cake-middle {
        width: 120px;
        height: 38px;
    }

    .cake-bottom {
        width: 150px;
        height: 45px;
    }

    .candles {
        gap: 8px;
        top: -28px;
    }

    .candle-stick {
        width: 4px;
        height: 18px;
    }

    .flame {
        width: 6px;
        height: 8px;
    }
}

@media (max-width: 480px) {
    .hero-container {
        padding: 0.5rem;
        gap: 2rem;
    }

    .hero-main-title {
        font-size: 2.8rem;
    }

    .hero-message {
        font-size: 1rem;
        margin-bottom: 2rem;
    }

    .hero-actions {
        flex-direction: column;
        gap: 1rem;
    }

    .primary-btn, .secondary-btn {
        padding: 1rem 1.5rem;
        font-size: 0.9rem;
        width: 100%;
        justify-content: center;
    }

    .celebration-card {
        padding: 1.5rem;
    }

    .countdown-display {
        gap: 0.2rem;
    }

    .time-block {
        padding: 0.6rem;
        min-width: 45px;
    }

    .time-number {
        font-size: 1.3rem;
    }

    .time-separator {
        font-size: 1.2rem;
    }

    .cake-close-btn {
        top: -30px;
        right: -30px;
        width: 35px;
        height: 35px;
    }

    .cake-message h2 {
        font-size: 1.8rem;
    }

    .cake-message p {
        font-size: 1rem;
    }

    .cake-top {
        width: 70px;
        height: 25px;
    }

    .cake-middle {
        width: 100px;
        height: 32px;
    }

    .cake-bottom {
        width: 130px;
        height: 40px;
    }

    .candles {
        gap: 6px;
        top: -25px;
    }

    .candle-stick {
        width: 3px;
        height: 15px;
    }

    .flame {
        width: 5px;
        height: 7px;
    }
}
